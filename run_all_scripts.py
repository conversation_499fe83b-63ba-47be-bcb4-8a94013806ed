#!/usr/bin/env python3
"""
Master <PERSON>: Run all three data collection scripts in sequence
"""

import subprocess
import sys
import time
import os

def run_script(script_name, description):
    """Run a Python script and handle errors"""
    print(f"\n{'='*60}")
    print(f"STARTING: {description}")
    print(f"Script: {script_name}")
    print(f"{'='*60}")
    
    try:
        # Run the script
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            print(f"✅ SUCCESS: {script_name} completed successfully")
            print("STDOUT:")
            print(result.stdout)
        else:
            print(f"❌ ERROR: {script_name} failed with return code {result.returncode}")
            print("STDOUT:")
            print(result.stdout)
            print("STDERR:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {script_name} timed out after 1 hour")
        return False
    except Exception as e:
        print(f"💥 EXCEPTION: Error running {script_name}: {str(e)}")
        return False
    
    return True

def check_file_exists(filename):
    """Check if a file exists and show its size"""
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        print(f"✅ File created: {filename} ({size} bytes)")
        return True
    else:
        print(f"❌ File not found: {filename}")
        return False

def main():
    """Run all scripts in sequence"""
    print("🚀 Starting Data Collection Pipeline")
    print("This will run 3 scripts in sequence to collect trade data")
    print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    scripts = [
        {
            'name': 'script1_hs_code_resolution.py',
            'description': 'HS Code Resolution for all chemicals',
            'output_file': 'hs_code_resolution.csv'
        },
        {
            'name': 'script2_geography_suppliers_tariff.py', 
            'description': 'Geography Suppliers with Tariff Data',
            'output_file': 'geography_suppliers_tariff.csv'
        },
        {
            'name': 'script3_enhanced_suppliers_count.py',
            'description': 'Enhanced Suppliers Count by Country',
            'output_file': 'enhanced_suppliers_count.csv'
        }
    ]
    
    # Track overall progress
    total_scripts = len(scripts)
    completed_scripts = 0
    
    for i, script_info in enumerate(scripts, 1):
        script_name = script_info['name']
        description = script_info['description']
        output_file = script_info['output_file']
        
        print(f"\n📊 PROGRESS: Script {i}/{total_scripts}")
        
        # Check if script file exists
        if not os.path.exists(script_name):
            print(f"❌ Script file not found: {script_name}")
            continue
        
        # Run the script
        success = run_script(script_name, description)
        
        if success:
            completed_scripts += 1
            # Check if output file was created
            check_file_exists(output_file)
            print(f"✅ Script {i} completed successfully")
        else:
            print(f"❌ Script {i} failed")
            print("⚠️  Continuing with next script...")
        
        # Add a small delay between scripts
        if i < total_scripts:
            print("⏳ Waiting 5 seconds before next script...")
            time.sleep(5)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📋 FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"Total scripts: {total_scripts}")
    print(f"Completed successfully: {completed_scripts}")
    print(f"Failed: {total_scripts - completed_scripts}")
    print(f"End time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check all output files
    print(f"\n📁 OUTPUT FILES:")
    for script_info in scripts:
        check_file_exists(script_info['output_file'])
    
    if completed_scripts == total_scripts:
        print(f"\n🎉 ALL SCRIPTS COMPLETED SUCCESSFULLY!")
        print("You now have 3 CSV files with trade data:")
        print("1. hs_code_resolution.csv - HS codes for all chemicals")
        print("2. geography_suppliers_tariff.csv - Top supplier countries with tariff data")
        print("3. enhanced_suppliers_count.csv - Supplier counts by country and time period")
    else:
        print(f"\n⚠️  PIPELINE COMPLETED WITH SOME FAILURES")
        print("Please check the error messages above and re-run failed scripts if needed.")

if __name__ == "__main__":
    main()
