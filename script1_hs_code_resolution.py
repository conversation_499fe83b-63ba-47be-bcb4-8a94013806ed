#!/usr/bin/env python3
"""
Script 1: HS Code Resolution
Hits GET /v1/code-resolver/resolve-enhanced for each chemical in historical_data.csv
"""

import pandas as pd
import requests
import time
import json
import logging
from urllib.parse import urlencode
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('script1_hs_code_resolution.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_historical_data():
    """Load the historical data CSV file"""
    try:
        df = pd.read_csv('app/data/historical_data.csv')
        logger.info(f"✅ Loaded {len(df)} chemicals from historical_data.csv")
        return df
    except Exception as e:
        logger.error(f"❌ Error loading historical data: {e}")
        return None

def resolve_hs_code(chemical_name, chemical_application, base_url="http://localhost:5001/pricing"):
    """
    Call the GET /v1/code-resolver/resolve-enhanced endpoint
    """
    try:
        # Prepare query parameters
        params = {
            'chemical_name': chemical_name,
            'chemical_application': chemical_application
        }
        
        # Construct URL with query parameters
        url = f"{base_url}/v1/code-resolver/resolve-enhanced"
        
        logger.info(f"🔄 Calling API for: {chemical_name}")
        logger.debug(f"URL: {url}")
        logger.debug(f"Params: {params}")

        # Make the GET request
        response = requests.get(url, params=params, timeout=30)

        if response.status_code == 200:
            data = response.json()
            hs_code = data.get('hs_code', '')
            logger.info(f"✅ Success for {chemical_name}: HS Code = {hs_code}")
            return {
                'status': 'success',
                'chemical_name': chemical_name,
                'hs_code': hs_code,
                'cas_number': data.get('cas_number', ''),
                'product_family': data.get('product_family', ''),
                'confidence_score': data.get('confidence_score', ''),
                'source_preference': data.get('source_preference', ''),
                'from_llm': data.get('from_llm', False)
            }
        else:
            logger.error(f"❌ API call failed for {chemical_name}: {response.status_code} - {response.text}")
            return {
                'status': 'failed',
                'chemical_name': chemical_name,
                'hs_code': '',
                'cas_number': '',
                'product_family': '',
                'confidence_score': '',
                'source_preference': '',
                'from_llm': False,
                'error': f"HTTP {response.status_code}: {response.text}"
            }

    except Exception as e:
        logger.error(f"💥 Exception for {chemical_name}: {str(e)}")
        return {
            'status': 'failed',
            'chemical_name': chemical_name,
            'hs_code': '',
            'cas_number': '',
            'product_family': '',
            'confidence_score': '',
            'source_preference': '',
            'from_llm': False,
            'error': str(e)
        }

def main():
    """Main function to process all chemicals and resolve HS codes"""
    start_time = datetime.now()
    logger.info("🚀 Starting HS Code Resolution Script...")
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Load historical data
    df = load_historical_data()
    if df is None:
        return

    # Prepare results list
    results = []
    success_count = 0
    failed_count = 0

    logger.info(f"📊 Processing {len(df)} chemicals...")

    # Process each chemical
    for index, row in df.iterrows():
        chemical_name = row['product_name']
        chemical_application = row['product_family']  # Using product_family as application

        logger.info(f"\n📋 Processing {index + 1}/{len(df)}: {chemical_name}")

        # Call the API
        result = resolve_hs_code(chemical_name, chemical_application)
        results.append(result)

        # Update counters
        if result['status'] == 'success':
            success_count += 1
        else:
            failed_count += 1

        # Log progress every 10 chemicals
        if (index + 1) % 10 == 0:
            logger.info(f"📈 Progress: {index + 1}/{len(df)} chemicals processed. Success: {success_count}, Failed: {failed_count}")

        # Add delay to avoid overwhelming the server
        time.sleep(2)

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)

    # Save to CSV
    output_file = 'hs_code_resolution.csv'
    results_df.to_csv(output_file, index=False)
    logger.info(f"💾 Results saved to {output_file}")

    # Calculate duration
    end_time = datetime.now()
    duration = end_time - start_time

    # Print final summary
    logger.info(f"\n🎯 FINAL SUMMARY:")
    logger.info(f"Total chemicals processed: {len(results_df)}")
    logger.info(f"✅ Successful resolutions: {success_count}")
    logger.info(f"❌ Failed resolutions: {failed_count}")
    logger.info(f"⏱️  Total duration: {duration}")
    logger.info(f"📊 Success rate: {(success_count/len(results_df)*100):.1f}%")

    # Show first few results
    logger.info(f"\n📋 First 5 results:")
    logger.info(f"\n{results_df.head().to_string()}")

if __name__ == "__main__":
    main()
