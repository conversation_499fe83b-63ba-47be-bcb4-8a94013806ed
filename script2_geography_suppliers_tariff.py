#!/usr/bin/env python3
"""
Script 2: Geography Suppliers with Tariff Data
Hits GET /v1/trade/top-suppliers-by-geography-tariff for each chemical with different month periods
"""

import pandas as pd
import requests
import time
import json
import logging
from urllib.parse import urlencode
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('script2_geography_suppliers_tariff.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_hs_code_results():
    """Load the HS code resolution results from Script 1"""
    try:
        df = pd.read_csv('hs_code_resolution.csv')
        # Filter only successful resolutions
        successful_df = df[df['status'] == 'success'].copy()
        logger.info(f"✅ Loaded {len(successful_df)} successful HS code resolutions out of {len(df)} total")
        return successful_df
    except Exception as e:
        logger.error(f"❌ Error loading HS code results: {e}")
        return None

def get_geography_suppliers_tariff(hs_code, chemical_name, months, base_url="http://localhost:5001/pricing"):
    """
    Call the GET /v1/trade/top-suppliers-by-geography-tariff endpoint
    """
    try:
        # Prepare query parameters
        params = {
            'hs_code': hs_code,
            'months': months,
            'destination': 'United States',
            'chemical_name': chemical_name
        }
        
        # Construct URL
        url = f"{base_url}/v1/trade/top-suppliers-by-geography-tariff"
        
        logger.info(f"🔄 Calling API for: {chemical_name} (HS: {hs_code}, Months: {months})")

        # Make the GET request
        response = requests.get(url, params=params, timeout=60)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Success for {chemical_name} ({months} months)")
            
            # Extract country data from the response
            result_data = data.get('result', {})
            countries_data = []

            # Get the data array from the result
            data_array = result_data.get('data', [])

            if isinstance(data_array, list):
                # Get total number of records for this response
                total_records = len(data_array)

                for country_info in data_array:
                    country_name = country_info.get('origin_country', '')
                    shipment_count = country_info.get('shipment_count', 0)
                    data_source = country_info.get('data_source', 'database')

                    # Determine if this is from database or LLM
                    is_from_llm = data_source == 'llm_research' or country_info.get('llm_used', False)

                    countries_data.append({
                        'chemical_name': chemical_name,
                        'hs_code': hs_code,
                        'months': months,
                        'country_name': country_name,
                        'from_db_name': country_name if not is_from_llm else '',
                        'from_db_number_of_shipments': shipment_count if not is_from_llm else '',
                        'from_llm_name': country_name if is_from_llm else '',
                        'from_llm_number_of_shipments': shipment_count if is_from_llm else '',
                        'data_source': 'llm' if is_from_llm else 'database',
                        'total_records': total_records,
                        'status': 'success'
                    })
            
            return countries_data
            
        else:
            logger.error(f"❌ API call failed for {chemical_name}: {response.status_code} - {response.text}")
            return [{
                'chemical_name': chemical_name,
                'hs_code': hs_code,
                'months': months,
                'country_name': '',
                'from_db_name': '',
                'from_db_number_of_shipments': '',
                'from_llm_name': '',
                'from_llm_number_of_shipments': '',
                'data_source': '',
                'total_records': 0,
                'status': 'failed',
                'error': f"HTTP {response.status_code}: {response.text}"
            }]

    except Exception as e:
        logger.error(f"💥 Exception for {chemical_name}: {str(e)}")
        return [{
            'chemical_name': chemical_name,
            'hs_code': hs_code,
            'months': months,
            'country_name': '',
            'from_db_name': '',
            'from_db_number_of_shipments': '',
            'from_llm_name': '',
            'from_llm_number_of_shipments': '',
            'data_source': '',
            'total_records': 0,
            'status': 'failed',
            'error': str(e)
        }]

def main():
    """Main function to process all chemicals for geography suppliers with tariff"""
    start_time = datetime.now()
    logger.info("🚀 Starting Geography Suppliers Tariff Script...")
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Load HS code results
    df = load_hs_code_results()
    if df is None:
        return

    # Prepare results list
    all_results = []

    # Month periods to query
    month_periods = [3, 6, 9, 12]

    # Process each chemical for each month period
    total_calls = len(df) * len(month_periods)
    current_call = 0
    success_calls = 0
    failed_calls = 0

    logger.info(f"📊 Processing {len(df)} chemicals × {len(month_periods)} time periods = {total_calls} total API calls")

    for index, row in df.iterrows():
        chemical_name = row['chemical_name']
        hs_code = row['hs_code']

        logger.info(f"\n📋 Processing chemical {index + 1}/{len(df)}: {chemical_name} (HS: {hs_code})")

        for months in month_periods:
            current_call += 1
            logger.info(f"🔄 API Call {current_call}/{total_calls}: {chemical_name} - {months} months")

            # Call the API
            results = get_geography_suppliers_tariff(hs_code, chemical_name, months)
            all_results.extend(results)

            # Count successes/failures
            for result in results:
                if result.get('status') == 'success':
                    success_calls += 1
                else:
                    failed_calls += 1

            # Log progress every 20 calls
            if current_call % 20 == 0:
                logger.info(f"📈 Progress: {current_call}/{total_calls} calls completed. Success: {success_calls}, Failed: {failed_calls}")

            # Add delay to avoid overwhelming the server
            time.sleep(2)
    
    # Convert results to DataFrame
    results_df = pd.DataFrame(all_results)

    # Save to CSV
    output_file = 'geography_suppliers_tariff.csv'
    results_df.to_csv(output_file, index=False)
    logger.info(f"💾 Results saved to {output_file}")

    # Calculate duration
    end_time = datetime.now()
    duration = end_time - start_time

    # Print summary
    if len(results_df) > 0:
        success_count = len(results_df[results_df['status'] == 'success'])
        failed_count = len(results_df[results_df['status'] == 'failed'])
    else:
        success_count = 0
        failed_count = 0

    logger.info(f"\n🎯 FINAL SUMMARY:")
    logger.info(f"Total API calls made: {len(results_df)}")
    logger.info(f"✅ Successful calls: {success_count}")
    logger.info(f"❌ Failed calls: {failed_count}")
    logger.info(f"⏱️  Total duration: {duration}")
    if len(results_df) > 0:
        logger.info(f"📊 Success rate: {(success_count/len(results_df)*100):.1f}%")

    # Show first few results
    logger.info(f"\n📋 First 5 results:")
    logger.info(f"\n{results_df.head().to_string()}")

if __name__ == "__main__":
    main()
