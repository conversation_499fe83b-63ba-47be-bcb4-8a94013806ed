# Trade Data Collection Scripts

This repository contains 3 Python scripts that collect trade data for chemicals listed in `app/data/historical_data.csv`.

## Prerequisites

1. **Start the API server**: Run `python main.py` to start the local API server
2. **Install dependencies**: Make sure you have `pandas` and `requests` installed:
   ```bash
   pip install pandas requests
   ```

## Scripts Overview

### Script 1: HS Code Resolution (`script1_hs_code_resolution.py`)
- **Purpose**: Resolves HS codes for all chemicals using the enhanced code resolver
- **Endpoint**: `GET /v1/code-resolver/resolve-enhanced`
- **Input**: Reads from `app/data/historical_data.csv`
- **Output**: Creates `hs_code_resolution.csv`
- **Process**: For each chemical, calls the API with `chemical_name` and `product_family` (as application)

### Script 2: Geography Suppliers with Tariff (`script2_geography_suppliers_tariff.py`)
- **Purpose**: Gets top supplier countries with tariff data for different time periods
- **Endpoint**: `GET /v1/trade/top-suppliers-by-geography-tariff`
- **Input**: Uses HS codes from Script 1 results
- **Output**: Creates `geography_suppliers_tariff.csv`
- **Process**: For each chemical, makes 4 API calls (3, 6, 9, 12 months)

### Script 3: Enhanced Suppliers Count (`script3_enhanced_suppliers_count.py`)
- **Purpose**: Gets supplier counts for each country/chemical/time period combination
- **Endpoint**: `POST /v1/trade/enhanced-top-suppliers`
- **Input**: Uses countries from Script 2 results
- **Output**: Creates `enhanced_suppliers_count.csv`
- **Process**: For each unique chemical-country pair, makes 4 API calls (3, 6, 9, 12 months) with limit=100

## Usage

### Option 1: Run All Scripts at Once (Recommended)
```bash
python run_all_scripts.py
```
This will run all 3 scripts in sequence and provide progress updates.

### Option 2: Run Scripts Individually
```bash
# Step 1: Resolve HS codes
python script1_hs_code_resolution.py

# Step 2: Get geography suppliers (requires step 1 to be completed)
python script2_geography_suppliers_tariff.py

# Step 3: Get supplier counts (requires step 2 to be completed)
python script3_enhanced_suppliers_count.py
```

## Output Files

### 1. `hs_code_resolution.csv`
Contains HS code resolution results for each chemical:
- `chemical_name`: Name of the chemical
- `hs_code`: Resolved HS code
- `cas_number`: CAS number (if available)
- `product_family`: Product family
- `confidence_score`: Confidence score of resolution
- `source_preference`: Data source used
- `from_llm`: Whether data came from LLM
- `status`: success/failed

### 2. `geography_suppliers_tariff.csv`
Contains top supplier countries with tariff data:
- `chemical_name`: Name of the chemical
- `hs_code`: HS code
- `months`: Time period (3, 6, 9, or 12 months)
- `country_name`: Country name
- `from_db_name`: Country name from database
- `from_db_number`: Count from database
- `from_llm_name`: Country name from LLM
- `from_llm_number`: Count from LLM
- `data_source`: Source of data (database/llm)
- `status`: success/failed

### 3. `enhanced_suppliers_count.csv`
Contains supplier counts by country and time period:
- `chemical_name`: Name of the chemical
- `hs_code`: HS code
- `country`: Supplier country
- `months`: Time period (3, 6, 9, or 12 months)
- `supplier_count`: Number of suppliers found
- `status`: success/failed

## Configuration

### API Base URL
By default, scripts use `http://localhost:5000`. To change this, modify the `base_url` parameter in each script.

### Rate Limiting
Scripts include a 2-second delay between API calls to avoid overwhelming the server. You can modify this in each script if needed.

### Error Handling
- All scripts include comprehensive error handling
- Failed API calls are logged with error details
- Scripts continue processing even if individual calls fail
- Status column indicates success/failure for each record

## Monitoring Progress

The scripts provide detailed console output including:
- Current chemical being processed
- API call details
- Success/failure status
- Progress counters
- Final summaries

## Troubleshooting

1. **API Server Not Running**: Make sure `python main.py` is running before starting the scripts
2. **Missing Dependencies**: Install required packages with `pip install pandas requests`
3. **File Not Found**: Ensure `app/data/historical_data.csv` exists
4. **API Timeouts**: Scripts have 30-second timeouts per API call
5. **Memory Issues**: Scripts process data incrementally to minimize memory usage

## Expected Runtime

- Script 1: ~2-3 minutes (54 chemicals × 2 seconds delay)
- Script 2: ~7-8 minutes (54 chemicals × 4 time periods × 2 seconds delay)
- Script 3: Variable depending on number of unique countries found in Script 2

Total estimated runtime: 15-20 minutes for all scripts.
