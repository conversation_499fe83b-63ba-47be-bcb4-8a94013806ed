#!/usr/bin/env python3
"""
Script 3: Enhanced Top Suppliers Count
Hits POST /v1/trade/enhanced-top-suppliers for each country/chemical/month combination

PURPOSE: For each country with VERIFIED TRADE DATA from Script 2, this script finds out
HOW MANY SUPPLIERS exist in that specific country for each chemical and time period (3, 6, 9, 12 months).

IMPORTANT: Only processes countries from DATABASE (actual trade records), NOT LLM research countries.

For example: If Script 2 found that India had 13 actual shipments of PAC to the US,
Script 3 will tell us how many different suppliers/companies in India are actually exporting PAC.
"""

import pandas as pd
import requests
import time
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('script3_enhanced_suppliers_count.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_geography_results():
    """Load the geography suppliers results from Script 2"""
    try:
        df = pd.read_csv('geography_suppliers_tariff.csv')
        # Filter only successful results and get unique combinations
        successful_df = df[df['status'] == 'success'].copy()
        logger.info(f"✅ Loaded {len(successful_df)} successful geography supplier records out of {len(df)} total")
        return successful_df
    except Exception as e:
        logger.error(f"❌ Error loading geography results: {e}")
        return None

def get_enhanced_suppliers_count(hs_code, country, chemical_name, months, base_url="http://localhost:5001/pricing"):
    """
    Call the POST /v1/trade/enhanced-top-suppliers endpoint
    """
    try:
        # Prepare request payload
        payload = {
            'hs_code': hs_code,
            'country': country,
            'destination': 'United States',
            'chemical_name': chemical_name,
            'months': months,
            'limit': 100
        }
        
        # Construct URL
        url = f"{base_url}/v1/trade/enhanced-top-suppliers"
        
        logger.info(f"🔄 Calling API for: {chemical_name} from {country} ({months} months)")

        # Make the POST request
        response = requests.post(url, json=payload, timeout=60)

        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Success for {chemical_name} from {country} ({months} months)")
            
            # Extract supplier count from the response
            result_data = data.get('data', [])
            supplier_count = 0
            
            if isinstance(result_data, list):
                supplier_count = len(result_data)
            elif isinstance(result_data, dict):
                # If the response has a different structure, try to get count
                supplier_count = result_data.get('count', 0)
                if supplier_count == 0 and 'suppliers' in result_data:
                    suppliers = result_data['suppliers']
                    if isinstance(suppliers, list):
                        supplier_count = len(suppliers)
            
            return {
                'chemical_name': chemical_name,
                'hs_code': hs_code,
                'country': country,
                'months': months,
                'supplier_count': supplier_count,
                'status': 'success'
            }
            
        else:
            logger.error(f"❌ API call failed for {chemical_name} from {country}: {response.status_code} - {response.text}")
            return {
                'chemical_name': chemical_name,
                'hs_code': hs_code,
                'country': country,
                'months': months,
                'supplier_count': 0,
                'status': 'failed',
                'error': f"HTTP {response.status_code}: {response.text}"
            }

    except Exception as e:
        logger.error(f"💥 Exception for {chemical_name} from {country}: {str(e)}")
        return {
            'chemical_name': chemical_name,
            'hs_code': hs_code,
            'country': country,
            'months': months,
            'supplier_count': 0,
            'status': 'failed',
            'error': str(e)
        }

def main():
    """Main function to process all country/chemical/month combinations"""
    start_time = datetime.now()
    logger.info("🚀 Starting Enhanced Suppliers Count Script...")
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Load geography results
    df = load_geography_results()
    if df is None:
        return

    # Prepare results list
    all_results = []

    # Month periods to query
    month_periods = [3, 6, 9, 12]

    # Get unique combinations of chemical and country from database results
    unique_combinations = []

    # Process ONLY countries from database (from_db_name is not empty)
    # Note: We only want supplier counts for countries with actual verified trade data
    logger.info("🔍 Filtering for DATABASE countries only (excluding LLM research countries)")

    db_countries = df[df['from_db_name'].notna() & (df['from_db_name'] != '')].copy()
    for _, row in db_countries.iterrows():
        chemical_name = row['chemical_name']
        hs_code = row['hs_code']
        country = row['from_db_name']

        if country and country.strip():  # Make sure country is not empty
            unique_combinations.append({
                'chemical_name': chemical_name,
                'hs_code': hs_code,
                'country': country.strip()
            })

    # SKIP LLM countries - we only want database countries for supplier counts
    logger.info("⏭️  Skipping LLM research countries - focusing only on verified trade data")

    # Remove duplicates
    unique_combinations_df = pd.DataFrame(unique_combinations).drop_duplicates()
    logger.info(f"📊 Found {len(unique_combinations_df)} unique chemical-country combinations")

    # Calculate total API calls
    total_calls = len(unique_combinations_df) * len(month_periods)
    current_call = 0
    success_calls = 0
    failed_calls = 0

    logger.info(f"📊 Processing {len(unique_combinations_df)} combinations × {len(month_periods)} time periods = {total_calls} total API calls")

    # Process each combination for each month period
    for index, row in unique_combinations_df.iterrows():
        chemical_name = row['chemical_name']
        hs_code = row['hs_code']
        country = row['country']

        logger.info(f"\n📋 Processing combination {index + 1}/{len(unique_combinations_df)}: {chemical_name} from {country}")

        for months in month_periods:
            current_call += 1
            logger.info(f"🔄 API Call {current_call}/{total_calls}: {chemical_name} from {country} - {months} months")

            # Call the API
            result = get_enhanced_suppliers_count(hs_code, country, chemical_name, months)
            all_results.append(result)

            # Count successes/failures
            if result.get('status') == 'success':
                success_calls += 1
            else:
                failed_calls += 1

            # Log progress every 20 calls
            if current_call % 20 == 0:
                logger.info(f"📈 Progress: {current_call}/{total_calls} calls completed. Success: {success_calls}, Failed: {failed_calls}")

            # Add delay to avoid overwhelming the server
            time.sleep(2)
    
    # Convert results to DataFrame
    results_df = pd.DataFrame(all_results)
    
    # Save to CSV
    output_file = 'enhanced_suppliers_count.csv'
    results_df.to_csv(output_file, index=False)
    logger.info(f"💾 Results saved to {output_file}")

    # Calculate duration
    end_time = datetime.now()
    duration = end_time - start_time

    # Print summary
    if len(results_df) > 0:
        success_count = len(results_df[results_df['status'] == 'success'])
        failed_count = len(results_df[results_df['status'] == 'failed'])
        total_suppliers = results_df[results_df['status'] == 'success']['supplier_count'].sum()
    else:
        success_count = 0
        failed_count = 0
        total_suppliers = 0

    logger.info(f"\n🎯 FINAL SUMMARY:")
    logger.info(f"Total API calls made: {len(results_df)}")
    logger.info(f"✅ Successful calls: {success_count}")
    logger.info(f"❌ Failed calls: {failed_count}")
    logger.info(f"📊 Total suppliers found: {total_suppliers}")
    logger.info(f"⏱️  Total duration: {duration}")
    if len(results_df) > 0:
        logger.info(f"📊 Success rate: {(success_count/len(results_df)*100):.1f}%")

    # Show first few results
    logger.info(f"\n📋 First 5 results:")
    logger.info(f"\n{results_df.head().to_string()}")

    # Show summary by chemical
    if success_count > 0:
        logger.info(f"\n📈 Supplier count summary by chemical:")
        summary = results_df[results_df['status'] == 'success'].groupby('chemical_name')['supplier_count'].sum().sort_values(ascending=False)
        logger.info(f"\n{summary.head(10).to_string()}")

if __name__ == "__main__":
    main()
